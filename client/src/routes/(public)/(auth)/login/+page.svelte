<script lang="ts">
	/**
	 * Login Page Component - Complete Implementation
	 *
	 * This component provides a complete login form with:
	 * - Zod schema validation using Superforms
	 * - Real-time form validation and error display
	 * - Integration with Django backend authentication via auth store
	 * - Loading states and user feedback
	 * - Proper error handling for different scenarios
	 * - Reactive authentication state management
	 */

	import { Button } from "$lib/components/ui/button";
	import { Input } from "$lib/components/ui/input";
	import { Label } from "$lib/components/ui/label";
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from "$lib/components/ui/card";
	import { Separator } from "$lib/components/ui/separator";
	import GoogleIcon from "$lib/components/icons/ui/GoogleIcon.svelte";
	import { BrandLogoIcon } from "$lib/components/icons/brand";
	import { goto } from "$app/navigation";
	import { superForm } from "sveltekit-superforms";
	import { zod } from "sveltekit-superforms/adapters";
	import { loginSchema } from "$lib/schemas/auth";
	import { getAuthContext } from "$lib/stores/auth.svelte";

	// Get authentication store from context
	const authStore = getAuthContext();

	// Initialize Superforms with Zod validation for client-side only submission
	const { form, errors, enhance, submitting } = superForm(
		{ email: "", password: "" },
		{
			validators: zod(loginSchema),
			SPA: true, // Enable SPA mode to prevent server-side form submission
			resetForm: false, // Keep form data on submission
			clearOnSubmit: 'errors-and-message', // Clear only errors on submit
			onSubmit: async ({ formData, cancel }) => {
				// Prevent default form submission since we handle it client-side
				cancel();

				// Clear any previous errors
				authStore.clearError();

				// Extract form data
				const email = formData.get("email") as string;
				const password = formData.get("password") as string;

				console.log("Login attempt:", { email, password: "***" }); // Debug log

				// Validate form data before submission
				if (!email || !password) {
					authStore.error = "Please fill in all fields";
					return;
				}

				// Attempt login through auth store
				const success = await authStore.login({ email, password });

				if (success) {
					// Redirect to dashboard or intended page on successful login
					goto("/dashboard");
				}
				// Error handling is managed by the auth store and displayed below
			}
		}
	);

	/**
	 * Handle form submission manually (backup method)
	 * This ensures proper POST method and prevents URL parameters
	 */
	async function handleFormSubmit(event: Event): Promise<void> {
		event.preventDefault(); // Prevent default form submission

		// Clear any previous errors
		authStore.clearError();

		// Validate form using Superforms
		const validation = loginSchema.safeParse($form);
		if (!validation.success) {
			// Set validation errors
			const fieldErrors = validation.error.flatten().fieldErrors;
			if (fieldErrors.email) errors.set({ ...$errors, email: fieldErrors.email });
			if (fieldErrors.password) errors.set({ ...$errors, password: fieldErrors.password });
			return;
		}

		console.log("Manual form submit:", { email: $form.email, password: "***" }); // Debug log

		// Attempt login through auth store
		const success = await authStore.login({
			email: $form.email,
			password: $form.password
		});

		if (success) {
			// Redirect to dashboard or intended page on successful login
			goto("/dashboard");
		}
		// Error handling is managed by the auth store and displayed below
	}

	/**
	 * Handle Google OAuth login
	 * TODO: Implement Google OAuth integration
	 */
	function handleGoogleLogin() {
		console.log("Google login requested");
	}

	/**
	 * Handle navigation to registration/contact sales
	 */
	function handleRegister() {
		goto("/contact-sales");
	}
</script>

<div class="flex min-h-[calc(100vh-3.25rem)] items-center justify-center p-4">
	<div class="w-full max-w-md">
		<!-- Logo -->
		<a href="/" class="mb-8 flex justify-center">
			<div class="bg-primary flex h-16 w-16 items-center justify-center rounded-2xl shadow-sm">
				<BrandLogoIcon class="text-accent p-3" />
			</div>
		</a>

		<!-- Login Card -->
		<Card class="border-0 shadow-lg">
			<CardHeader class="space-y-2 text-center">
				<CardTitle class="text-2xl font-semibold">Welcome back!</CardTitle>
				<CardDescription class="text-slate-600">
					Please enter your details to login.
				</CardDescription>
			</CardHeader>

			<CardContent class="space-y-6">
				<!-- Social Login Buttons -->
				<div class="space-y-3">
					<Button
						variant="outline"
						class="h-12 w-full cursor-pointer text-slate-700"
						onclick={handleGoogleLogin}
					>
						<GoogleIcon class="size-5" />
						Login with Google
					</Button>
				</div>

				<!-- Divider -->
				<div class="relative">
					<div class="absolute inset-0 flex items-center">
						<Separator />
					</div>
					<div class="relative flex justify-center text-xs uppercase">
						<span class="bg-white px-2 text-slate-500">OR</span>
					</div>
				</div>

				<!-- Email Login Form with Manual Submit Handler -->
				<form
					method="POST"
					onsubmit={handleFormSubmit}
					class="space-y-4"
				>
					<div class="space-y-2">
						<Label for="email">Email</Label>
						<Input
							id="email"
							name="email"
							type="email"
							placeholder="<EMAIL>"
							class={`h-12 ${$errors.email ? "border-red-500" : ""}`}
							bind:value={$form.email}
							aria-invalid={$errors.email ? "true" : undefined}
							required
						/>
						{#if $errors.email}
							<p class="text-sm text-red-600">{$errors.email}</p>
						{/if}
					</div>

					<div class="space-y-2">
						<Label for="password">Password</Label>
						<Input
							id="password"
							name="password"
							type="password"
							placeholder="••••••••••"
							class={`h-12 ${$errors.password ? "border-red-500" : ""}`}
							bind:value={$form.password}
							aria-invalid={$errors.password ? "true" : undefined}
							required
						/>
						{#if $errors.password}
							<p class="text-sm text-red-600">{$errors.password}</p>
						{/if}
					</div>

					<!-- Display authentication errors from the store -->
					{#if authStore.error}
						<div class="rounded-md bg-red-50 p-3">
							<p class="text-sm text-red-800">{authStore.error}</p>
						</div>
					{/if}

					<Button
						type="submit"
						class="h-12 w-full cursor-pointer bg-black hover:bg-slate-800"
						disabled={$submitting || authStore.isLoading}
					>
						{#if $submitting || authStore.isLoading}
							<div class="flex items-center gap-2">
								<div
									class="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"
								></div>
								Signing in...
							</div>
						{:else}
							Sign in
						{/if}
					</Button>
				</form>

				<!-- Reset Password Link -->
				<div class="text-center">
					<span class="text-sm text-slate-600">Forgot your password? </span>
					<a
						class="cursor-pointer text-sm font-medium text-slate-900 hover:underline"
						href="/reset-password"
					>
						Reset it
					</a>
				</div>
			</CardContent>
		</Card>

		<!-- Register Link -->
		<div class="mt-6 text-center">
			<span class="text-sm text-slate-600">Don't have an account? </span>
			<button
				class="cursor-pointer text-sm font-medium text-slate-900 hover:underline"
				onclick={handleRegister}
			>
				Register
			</button>
		</div>
	</div>
</div>
