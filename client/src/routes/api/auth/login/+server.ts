/**
 * Server-side API route for authentication login
 * 
 * This route acts as a proxy to the Django backend, avoiding CORS issues
 * by making the API request from the server side instead of the client side.
 * 
 * Dependencies: None (uses native fetch)
 * Used by: Auth service for login requests
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

const API_BASE_URL = 'https://refactored-memory.onrender.com';

export const POST: RequestHandler = async ({ request }) => {
	try {
		// Get the request body from the client
		const body = await request.json();
		
		console.log('Server API: Proxying login request to:', `${API_BASE_URL}/auth/login/`);
		console.log('Server API: Request body:', { email: body.email, password: '***' });

		// Make the request to the Django backend
		const response = await fetch(`${API_BASE_URL}/auth/login/`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(body)
		});

		console.log('Server API: Backend response status:', response.status);

		// Get the response data
		const data = await response.json();
		console.log('Server API: Backend response data:', data);

		// Return the response with the same status code
		return json(data, { 
			status: response.status,
			headers: {
				'Content-Type': 'application/json'
			}
		});

	} catch (error) {
		console.error('Server API: Error proxying login request:', error);
		
		return json(
			{ 
				detail: 'Internal server error during authentication',
				error: error instanceof Error ? error.message : 'Unknown error'
			}, 
			{ status: 500 }
		);
	}
};
