/**
 * Authentication API Service
 *
 * This file handles all API communication with the Django backend for
 * authentication operations. Includes login, token refresh, and user
 * data retrieval with proper error handling and type safety.
 *
 * Dependencies: types/auth.ts, utils/cookies.ts
 * Used by: Auth store, login components
 */

import type {
	LoginCredentials,
	LoginResponse,
	TokenRefreshResponse,
	User,
	ApiResponse,
	AuthError
} from "$types/auth";
import {
	getRefreshToken,
	setAccessToken,
	setRefreshToken,
	clearAuthTokens
} from "$lib/utils/cookies";

/**
 * Base API configuration
 * Use local API routes to avoid CORS issues
 */
const API_BASE_URL = "https://refactored-memory.onrender.com";
const AUTH_ENDPOINTS = {
	login: "/api/auth/login", // Use local proxy route
	refresh: "/auth/token/refresh/", // Direct to backend (will need proxy if CORS issues)
	user: "/auth/users/" // For fetching user profile (will need proxy if CORS issues)
} as const;

/**
 * API Error class for structured error handling
 */
export class ApiError extends Error {
	constructor(
		message: string,
		public status: number,
		public data?: AuthError
	) {
		super(message);
		this.name = "ApiError";
	}
}

/**
 * Make authenticated API request with automatic token refresh
 *
 * @param url - API endpoint URL
 * @param options - Fetch options
 * @returns Promise with API response
 */
async function apiRequest<T>(url: string, options: RequestInit = {}): Promise<T> {
	// Determine if this is a local API route or external API
	const isLocalRoute = url.startsWith("/api/");
	const fullUrl = isLocalRoute ? url : `${API_BASE_URL}${url}`;

	console.log("API Request: Making request to:", fullUrl); // Debug log
	console.log("API Request: Is local route:", isLocalRoute); // Debug log
	console.log("API Request: Options:", {
		method: options.method || "GET",
		headers: options.headers,
		body: options.body ? "***" : undefined
	}); // Debug log

	try {
		const response = await fetch(fullUrl, {
			...options,
			headers: {
				"Content-Type": "application/json",
				...options.headers
			}
		});

		console.log("API Request: Response status:", response.status); // Debug log
		console.log("API Request: Response headers:", Object.fromEntries(response.headers.entries())); // Debug log

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			console.log("API Request: Error data:", errorData); // Debug log
			throw new ApiError(`API request failed: ${response.status}`, response.status, errorData);
		}

		const data = await response.json();
		console.log("API Request: Success data:", data); // Debug log
		return data;
	} catch (error) {
		console.error("API Request: Network error:", error); // Debug log
		if (error instanceof ApiError) {
			throw error;
		}
		// Re-throw network errors with more context
		throw new Error(`Network error: ${error instanceof Error ? error.message : "Unknown error"}`);
	}
}

/**
 * Login user with email and password
 * Returns JWT tokens and user data on success
 *
 * @param credentials - User login credentials
 * @returns Promise with login response
 */
export async function loginUser(
	credentials: LoginCredentials
): Promise<ApiResponse<LoginResponse>> {
	try {
		console.log("Auth service: Making login request to:", `${API_BASE_URL}${AUTH_ENDPOINTS.login}`); // Debug log
		console.log("Auth service: Login credentials:", { email: credentials.email, password: "***" }); // Debug log

		const response = await apiRequest<LoginResponse>(AUTH_ENDPOINTS.login, {
			method: "POST",
			body: JSON.stringify(credentials)
		});

		console.log("Auth service: Login successful, storing tokens"); // Debug log

		// Store tokens in secure cookies
		setAccessToken(response.access);
		setRefreshToken(response.refresh);

		return {
			success: true,
			data: response,
			message: "Login successful"
		};
	} catch (error) {
		console.error("Auth service: Login error:", error); // Debug log

		if (error instanceof ApiError) {
			console.log("Auth service: API error details:", {
				status: error.status,
				message: error.message,
				data: error.data
			}); // Debug log

			return {
				success: false,
				error: error.data,
				message: error.message
			};
		}

		// Handle network errors or other unexpected errors
		console.error("Auth service: Unexpected error during login:", error); // Debug log
		return {
			success: false,
			message: "Network error occurred"
		};
	}
}

/**
 * Refresh JWT access token using refresh token
 * Automatically updates access token cookie on success
 *
 * @returns Promise with refresh response
 */
export async function refreshAccessToken(): Promise<ApiResponse<TokenRefreshResponse>> {
	try {
		const refreshToken = getRefreshToken();

		if (!refreshToken) {
			throw new ApiError("No refresh token available", 401);
		}

		const response = await apiRequest<TokenRefreshResponse>(AUTH_ENDPOINTS.refresh, {
			method: "POST",
			body: JSON.stringify({ refresh: refreshToken })
		});

		// Update access token cookie
		setAccessToken(response.access);

		return {
			success: true,
			data: response,
			message: "Token refreshed successfully"
		};
	} catch (error) {
		// Clear tokens on refresh failure
		clearAuthTokens();

		if (error instanceof ApiError) {
			return {
				success: false,
				error: error.data,
				message: "Session expired. Please login again."
			};
		}

		return {
			success: false,
			message: "Failed to refresh session"
		};
	}
}

/**
 * Logout user by clearing authentication tokens
 * Note: Django backend doesn't require server-side logout for JWT
 */
export function logoutUser(): void {
	clearAuthTokens();
}

/**
 * Get current user profile data
 * Requires valid authentication token
 *
 * @param userId - User ID to fetch profile for
 * @returns Promise with user data
 */
export async function getCurrentUser(userId: string): Promise<ApiResponse<User>> {
	try {
		const response = await apiRequest<User>(`${AUTH_ENDPOINTS.user}${userId}/`);

		return {
			success: true,
			data: response,
			message: "User data retrieved successfully"
		};
	} catch (error) {
		if (error instanceof ApiError) {
			return {
				success: false,
				error: error.data,
				message: error.message
			};
		}

		return {
			success: false,
			message: "Failed to fetch user data"
		};
	}
}

/**
 * Decode JWT token payload (client-side only for user info)
 * Note: This doesn't verify the token signature
 *
 * @param token - JWT token to decode
 * @returns Decoded token payload or null
 */
export function decodeJwtPayload(token: string): any | null {
	try {
		const base64Url = token.split(".")[1];
		const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
		const jsonPayload = decodeURIComponent(
			atob(base64)
				.split("")
				.map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
				.join("")
		);

		return JSON.parse(jsonPayload);
	} catch (error) {
		console.error("Failed to decode JWT token:", error);
		return null;
	}
}

/**
 * Check if JWT token is expired
 *
 * @param token - JWT token to check
 * @returns True if token is expired
 */
export function isTokenExpired(token: string): boolean {
	const payload = decodeJwtPayload(token);
	if (!payload || !payload.exp) {
		return true;
	}

	// JWT exp is in seconds, Date.now() is in milliseconds
	return Date.now() >= payload.exp * 1000;
}
