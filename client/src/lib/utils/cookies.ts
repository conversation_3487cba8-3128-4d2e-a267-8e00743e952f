/**
 * Cookie Utilities for JWT Token Management
 * 
 * This file provides utilities for secure cookie management, specifically
 * designed for JWT token storage. Implements secure, HttpOnly cookie handling
 * for authentication tokens with proper expiration and security settings.
 * 
 * Dependencies: None (vanilla JavaScript)
 * Used by: Auth store, auth service
 */

/**
 * Cookie configuration options
 */
interface CookieOptions {
	expires?: Date;
	maxAge?: number;
	path?: string;
	domain?: string;
	secure?: boolean;
	httpOnly?: boolean;
	sameSite?: 'strict' | 'lax' | 'none';
}

/**
 * Default cookie options for JWT tokens
 * Configured for maximum security in production, with development considerations
 */
const defaultTokenOptions: CookieOptions = {
	path: "/",
	secure: typeof window !== "undefined" && window.location.protocol === "https:", // HTTPS only in production
	httpOnly: false, // Set to false for client-side access in SPA mode
	sameSite: "lax" // CSRF protection with better compatibility
};

/**
 * Set a cookie with the specified name, value, and options
 * 
 * @param name - <PERSON><PERSON> name
 * @param value - Cookie value
 * @param options - Cookie configuration options
 */
export function setCookie(name: string, value: string, options: CookieOptions = {}): void {
	const opts = { ...defaultTokenOptions, ...options };
	
	let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;
	
	if (opts.expires) {
		cookieString += `; expires=${opts.expires.toUTCString()}`;
	}
	
	if (opts.maxAge) {
		cookieString += `; max-age=${opts.maxAge}`;
	}
	
	if (opts.path) {
		cookieString += `; path=${opts.path}`;
	}
	
	if (opts.domain) {
		cookieString += `; domain=${opts.domain}`;
	}
	
	if (opts.secure) {
		cookieString += '; secure';
	}
	
	if (opts.httpOnly) {
		cookieString += '; httponly';
	}
	
	if (opts.sameSite) {
		cookieString += `; samesite=${opts.sameSite}`;
	}
	
	document.cookie = cookieString;
}

/**
 * Get a cookie value by name
 * Returns null if cookie doesn't exist
 * 
 * @param name - Cookie name to retrieve
 * @returns Cookie value or null
 */
export function getCookie(name: string): string | null {
	const nameEQ = encodeURIComponent(name) + '=';
	const cookies = document.cookie.split(';');
	
	for (let cookie of cookies) {
		cookie = cookie.trim();
		if (cookie.indexOf(nameEQ) === 0) {
			return decodeURIComponent(cookie.substring(nameEQ.length));
		}
	}
	
	return null;
}

/**
 * Delete a cookie by setting its expiration to the past
 * 
 * @param name - Cookie name to delete
 * @param options - Additional cookie options (path, domain)
 */
export function deleteCookie(name: string, options: Partial<CookieOptions> = {}): void {
	setCookie(name, '', {
		...options,
		expires: new Date(0),
		maxAge: 0
	});
}

/**
 * Set JWT access token in secure cookie
 * Expires in 5 minutes (typical JWT access token lifetime)
 * 
 * @param token - JWT access token
 */
export function setAccessToken(token: string): void {
	setCookie('access_token', token, {
		maxAge: 5 * 60 // 5 minutes in seconds
	});
}

/**
 * Set JWT refresh token in secure cookie
 * Expires in 7 days (typical JWT refresh token lifetime)
 * 
 * @param token - JWT refresh token
 */
export function setRefreshToken(token: string): void {
	setCookie('refresh_token', token, {
		maxAge: 7 * 24 * 60 * 60 // 7 days in seconds
	});
}

/**
 * Get JWT access token from cookie
 * 
 * @returns Access token or null
 */
export function getAccessToken(): string | null {
	return getCookie('access_token');
}

/**
 * Get JWT refresh token from cookie
 * 
 * @returns Refresh token or null
 */
export function getRefreshToken(): string | null {
	return getCookie('refresh_token');
}

/**
 * Clear all authentication tokens
 * Removes both access and refresh tokens from cookies
 */
export function clearAuthTokens(): void {
	deleteCookie('access_token');
	deleteCookie('refresh_token');
}

/**
 * Check if user has valid authentication tokens
 * Note: This only checks for token existence, not validity
 * 
 * @returns True if both tokens exist
 */
export function hasAuthTokens(): boolean {
	return !!(getAccessToken() && getRefreshToken());
}
